import { Avatar, AvatarFallback, AvatarImage } from '@repo/shared/components/ui/avatar'
import TopLoadingBar from '@/components/TopLoadingBar'
import { Button } from '@repo/shared/components/ui/button'
import { Input } from '@repo/shared/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Switch } from '@repo/shared/components/ui/switch'
import { ValueSourcePicker } from '@repo/shared/lib/dynamic-values/components/ValueSourcePicker'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import {
    ArrowUpFromLine,
    BotIcon,
    Cast,
    ChevronDown,
    ChevronLeft,
    ChevronRight,
    Computer,
    Globe,
    GripVertical,
    HandMetalIcon,
    ImagesIcon,
    LayoutGrid,
    Loader2,
    Maximize,
    MonitorIcon,
    MoreHorizontal,
    MoreVertical,
    MoreVerticalIcon,
    Pencil,
    PhoneIcon,
    Play,
    Plus,
    SendHorizonal,
    SendHorizontal,
    SendIcon,
    Settings,
    SettingsIcon,
    Share,
    Share2,
    Smartphone,
    StarIcon,
    TabletIcon,
    TicketIcon,
    TrophyIcon,
    TvIcon,
    TwitterIcon,
    Zap,
    ZapIcon,
} from 'lucide-react'
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { IntegrationsManager } from '@/components/editor/IntegrationsManager'
import { VariablesEditor } from '@/components/editor/VariablesEditor'
import { Card, CardContent } from '@repo/shared/components/ui/card'
import { Label } from '@repo/shared/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@repo/shared/components/ui/popover'
import { Slider } from '@repo/shared/components/ui/slider'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/shared/components/ui/tabs'
import { toast, useToast } from '@repo/shared/components/ui/use-toast'
import { put, useApiMutation, useFetch } from '@repo/shared/lib/hooks/useApi'
import { CurrentOrganizationProvider, useCurrentOrganization } from '@repo/shared/lib/hooks/useCurrentOrganization'
import { CampaignEditorDto, CampaignSaveMode, CampaignScene, CampaignSceneType, CampaignUpdateDto } from '@repo/shared/lib/types/campaign'
import { loadGameModule, loadGameModules } from '@repo/shared/lib/game/gameRegistry'
import { cn, createShortUuid, getErrorMessage } from '@repo/shared/lib/utils'
import { DialogTitle } from '@radix-ui/react-dialog'
import { CheckIcon, CopyIcon, DownloadIcon, Loader2Icon, PackageIcon, PlayIcon, PlusIcon, SaveIcon, ShareIcon, Trash2, Undo2Icon } from 'lucide-react'
import QRCode from 'react-qr-code'
import { useParams } from 'react-router-dom'
import { useDebounce } from '@uidotdev/usehooks'
import { useInterval } from 'usehooks-ts'
import { debounce } from 'lodash'
import { current, produce } from 'immer'
import { Updater, useImmer } from 'use-immer'
import { useHistoryManager } from '../../lib/hooks/historyManager'
import SceneEditor from '../campaign/editor/_components/uiSceneEditor'
import AuthorizeView from '@/components/AuthorizeView'
import { ToggleGroup, ToggleGroupItem } from '@repo/shared/components/ui/toggle-group'
import { Tooltip, TooltipContent, TooltipTrigger } from '@repo/shared/components/ui/tooltip'
import { TooltipPortal, TooltipProvider } from '@radix-ui/react-tooltip'
import { Code, ShareCampaignDialog } from './_components/shareCampaignDialog'
import { CampaignEditorProvider, useCampaignEditor } from '@/lib/hooks/useCampaignEditor'
import { Badge } from '@repo/shared/components/ui/badge'
import { Widget } from '@repo/shared/lib/types/editor'
import { FontsLoader } from '@repo/shared/components/FontsLoader'
import { CampaignDataProvider, useCampaignData } from '@repo/shared/lib/hooks/useCampaignStore'
import AssetManagerDialog from '@repo/shared/components/editor/assetsManager'
import CreateNewCouponPack from '@/components/editor/CouponPackSection'
import CouponPacksManager from '@/components/editor/CouponPackSection'
import OrganizationMembers from '@/components/CampaignMembers'
import { useOrganizations } from '@/lib/hooks/useOrganizations'
import { DomainSettings } from '@/Pages/testpage/_components/domainSettings'
import { CustomDomainDto } from '@/Pages/home/<USER>/workspaceSettings'
import { useCampaignTemplates } from '@/lib/hooks/useCampaignTemplates'
import { EditorProvider } from '@/lib/hooks/useEditor'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTrigger } from '@repo/shared/components/ui/dialog'
import { LeaderboardsTab } from './_components/leaderboards/LeaderboardsTab'
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { arrayMove, sortableKeyboardCoordinates, rectSortingStrategy, SortableContext, useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { EditorPortal } from '@/components/EditorPortal'
import { CampaignFlow, CampaignFlowNode } from '@repo/shared/lib/campaign/actionNode'
import { useEventListener } from '@/lib/hooks/useEditorEventBus'
import { useAtom } from 'jotai'
import { currentSceneIndexAtom, selectedEditorItemAtom } from '@repo/shared/lib/atoms/editor-atoms'
import { DynamicValuesProvider } from '@repo/shared/lib/dynamic-values/provider'
import { useDynamicValues } from '@repo/shared/lib/dynamic-values/hooks/useDynamicValues'
import { FlowEditor } from '@/components/editor/flow-editor-new/FlowEditorNew'
import RewardSetDialog from '@repo/shared/features/rewards/components/RewardSetDialog'
import { useCampaignRewardSet } from '@repo/shared/features/rewards/lib/useCampaignRewardSet'

const CampaignEditor = () => {
    const { id } = useParams()

    return (
        <AuthorizeView>
            <CampaignEditorProvider campaignId={id}>
                <Page />
            </CampaignEditorProvider>
        </AuthorizeView>
    )
}



const Page = () => {
    const { isLoading, error, data, scenes, activeTab, setActiveTab, setSelectedFlowId } = useCampaignEditor()
    const location = useLocation()
    const { organizations } = useOrganizations()
    const organization = organizations?.find((o) => o.id === data?.workspaceId)

    const [isSettingsOpen, setIsSettingsOpen] = useState(false)
    const [initialSettingsTab, setInitialSettingsTab] = useState<'general' | 'domains' | 'leaderboards'>('general')


    const editor = useMemo(() => {
        loadGameModules()
    }, [])

    useEventListener('editor:show_flow', (event) => {
        console.log('Editor show flow. Switching to logic tab.', event.flowId)
        setActiveTab('logic')
        setSelectedFlowId(event.flowId)
    })

    useEffect(() => {
        if (!isSettingsOpen && location.hash === '#domain-settings') {
            window.history.replaceState(null, '', location.pathname + location.search)
        }
    }, [isSettingsOpen, location.hash, location.pathname, location.search])

    useEffect(() => {
        // Check the URL hash on mount
        if (location.hash === '#domain-settings') {
            setInitialSettingsTab('domains')
            setIsSettingsOpen(true)
        }
    }, [location.hash])

    useEffect(() => {
        console.log('[1] Campaign data changed ')
    }, [data])

    if (isLoading) return <TopLoadingBar />
    if (error) return <div>Error: {error.message}</div>
    if (!scenes) return <TopLoadingBar />

    return (
        <CurrentOrganizationProvider organization={organization}>
            <FontsLoader scenes={scenes} />
            <CampaignDataProvider
                campaignData={{
                    campaign: data,
                    hasParticipated: false,
                    assets: [],
                }}
            >
                <DynamicValuesProvider>

                <Tabs defaultValue="design" value={activeTab} onValueChange={setActiveTab} className="">
                    <div className="h-[100dvh] flex flex-col px-2 pb-1 ">
                        <div className=" flex-shrink-0 ">
                            <Header />
                        </div>

                        <div className="flex flex-row w-full flex-grow pb-2 overflow-hidden">
                            <ScenesEditorWithProvider activeTab={activeTab} />
                        </div>
                    </div>
                </Tabs>
                </DynamicValuesProvider>
            </CampaignDataProvider>
        </CurrentOrganizationProvider>
    )
}

const AddFlowDialog = ({ open, onOpenChange, onAdd }: { open: boolean; onOpenChange: (open: boolean) => void; onAdd: (name: string) => void }) => {
    const [name, setName] = useState('')

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Add New Flow</DialogTitle>
                    <DialogDescription>Enter a name for the new flow</DialogDescription>
                </DialogHeader>

                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="name" className="text-right">
                            Name
                        </Label>
                        <Input id="name" value={name} onChange={(e) => setName(e.target.value)} className="col-span-3" placeholder="Enter flow name" />
                    </div>
                </div>
                <DialogFooter>
                    <Button
                        onClick={() => {
                            if (name.trim()) {
                                onAdd(name.trim())
                                setName('')
                                onOpenChange(false)
                            }
                        }}
                    >
                        Add Flow
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

const ScenesEditorWithProvider = ({ activeTab }: { activeTab: 'logic' | 'design' }) => {
    const { scenes, setScenes, viewMode } = useCampaignEditor()
    const { flows } = useCampaignEditor()
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const [sceneToDelete, setSceneToDelete] = useState<string | null>(null)

    const [currentSceneIndex, setCurrentSceneIndex] = useAtom(currentSceneIndexAtom)
    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)

    const currentScene = useMemo(() => scenes[currentSceneIndex], [currentSceneIndex, scenes])

    const rootWidget = useMemo(() => {
        console.log('Current scene changed.')
        return currentScene?.rootWidget
    }, [currentScene])

    // Debounced setRootWidget using lodash debounce (300ms delay)
    const setRootWidget = useCallback(
        debounce(
            (widgetUpdater: Updater<Widget>) => {
                setScenes((old) => {
                    if (!old) return old
                    const existingScreen = old.find((s) => s.id === currentScene.id)
                    if (existingScreen) {
                        const result = (typeof widgetUpdater === 'function' ? widgetUpdater(existingScreen.rootWidget) : widgetUpdater) as unknown
                        if (result !== undefined) {
                            existingScreen.rootWidget = result as Widget
                        }
                    } else {
                        console.log('No existing screen or its root widget', existingScreen)
                    }
                    return old
                })
            },
            1,
            { maxWait: 600 }
        ),
        [currentScene, setScenes]
    )

    return (
        <EditorProvider rootWidget={rootWidget} setRootWidget={setRootWidget}>
            <div className={`flex-grow ${activeTab === 'design' ? '' : 'hidden'}`}>
                <ScenesEditor />
            </div>
            <div className={`flex-grow ${activeTab === 'logic' ? '' : 'hidden'}`}>
                <FlowEditorWrapper />
            </div>
        </EditorProvider>
    )
}

const FlowEditorWrapper = () => {
    const { flows, setFlows, saveChanges, data, setActiveTab, selectedFlowId: _selectedFlowId, setSelectedFlowId } = useCampaignEditor()
    const [isAddFlowDialogOpen, setIsAddFlowDialogOpen] = useState(false)
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
    const [redraw, setRedraw] = useState(0)

    console.log('Selected flow id: ', _selectedFlowId)
    const selectedFlowId = _selectedFlowId ?? flows[0]?.id

    useEventListener('editor:flow_changed', () => {
        console.log('Editor flow changed. Redrawing.')
        setRedraw((prev) => prev + 1)
    })

    const handleFlowChange = (flowId: string) => {
        setSelectedFlowId(flowId)
    }

    const handleAddFlow = (name: string) => {
        const newFlow = {
            id: createShortUuid(),
            name: name,
            nodes: [],
        }
        console.log('setflows 7')
        setFlows((draft) => {
            draft.push(newFlow)
        })
    }

    const handleDeleteFlow = () => {
        if (!selectedFlowId) return

        console.log('setflows 8')
        setFlows((draft) => {
            const index = draft.findIndex((f) => f.id === selectedFlowId)
            if (index !== -1) {
                draft.splice(index, 1)
            }
        })

        // Select the first flow after deletion
        if (flows.length > 1) {
            const nextFlow = flows[0]?.id === selectedFlowId ? flows[1] : flows[0]
            setSelectedFlowId(nextFlow?.id)
        }

        setIsDeleteDialogOpen(false)
        toast({
            title: 'Flow deleted',
            description: `Flow "${selectedFlowId}" has been deleted.`,
        })
    }

    const handleFlowUpdate = (updatedFlow: CampaignFlow) => {

        setFlows((draft) => {
            const index = draft.findIndex((f) => f.id === updatedFlow.id)
            if (index !== -1) {
                draft[index] = updatedFlow
            }
        })
    }

    useEffect(() => {
        console.log('flows changed 2', flows)
    }, [flows])

    return (
        <div className="relative h-full">
            <div className="flex h-full space-x-2">
                {/* Left Column */}
                <div className="w-[280px] bg-card text-foreground p-4 space-y-4 rounded-sm overflow-auto scroll-sm">
                    <VariablesEditor />
                </div>

                {/* Middle Column */}
                <div className="flex flex-col bg-card flex-grow  overflow-hidden ">
                    {/* Flow selector */}
                    <div className="pb-4 items-center flex gap-3">
                        <Select value={selectedFlowId || flows[0]?.id} onValueChange={handleFlowChange}>
                            <SelectTrigger className="w-[200px]">
                                <SelectValue placeholder="Select flow" />
                            </SelectTrigger>
                            <SelectContent>
                                {flows?.map((flow) => (
                                    <SelectItem key={flow.id} value={flow.id}>
                                        {flow.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <Button onClick={() => setIsAddFlowDialogOpen(true)} variant="outline" size="lg">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Flow
                        </Button>
                        <Button onClick={() => setIsDeleteDialogOpen(true)} variant="destructive" size="icon" disabled={flows.length <= 1}>
                            <Trash2 className="h-4 w-4" />
                        </Button>
                    </div>

                    <div className="flex flex-grow flex-row rounded-sm overflow-clip ">
                        <FlowEditor key={`${selectedFlowId}-${redraw}`} campaignFlow={flows.find((f) => f.id === selectedFlowId)} setFlow={handleFlowUpdate} />
                    </div>
                </div>

                {/* Right Column */}
                <div className="w-[280px] flex-none bg-card text-foreground p-4 space-y-4 rounded-sm overflow-auto scroll-sm">
                    <div className="flex flex-grow items-center justify-between border-b pb-2 mb-2">
                        <div className="font-bold text-sm">Nodes</div>
                    </div>
                </div>
            </div>
            <AddFlowDialog open={isAddFlowDialogOpen} onOpenChange={setIsAddFlowDialogOpen} onAdd={handleAddFlow} />
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete Flow</DialogTitle>
                        <DialogDescription>Are you sure you want to delete the flow "{flows.find((f) => f.id === selectedFlowId)?.name}"? This action cannot be undone.</DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleDeleteFlow}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}

const SortablePage = ({
    scene,
    index,
    isSelected,
    handleSceneChange,
    deleteScene,
}: {
    scene: CampaignScene
    index: number
    isSelected: boolean
    handleSceneChange: (value: string) => void
    deleteScene: (id: string) => void
}) => {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: scene.id })

    const { scenes, setScenes } = useCampaignEditor()

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    }

    return (
        <div ref={setNodeRef} style={style} className={cn('w-full p-0 rounded-md flex items-center group overflow-y-auto', isSelected ? 'bg-accent' : 'hover:bg-accent/50')}>
            {/* Drag Handle */}
            <div {...attributes} {...listeners} className="ps-2  cursor-move opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-in-out rounded">
                <GripVertical className="h-4 w-4  text-muted-foreground" />
            </div>

            {/* Scene Selection Area */}
            <div className="flex-1 flex justify-between items-center" onClick={() => handleSceneChange(scene.id)}>
                <Input
                    className="h-7  text-[0.85rem] bg-transparent border-none cursor-default focus:bg-black"
                    defaultValue={scene.displayName || scene.id}
                    readOnly={true}
                    onDoubleClick={(e) => {
                        e.stopPropagation()
                        e.currentTarget.readOnly = false
                        e.currentTarget.focus()
                    }}
                    onMouseDown={(e) => {
                        if (e.currentTarget.matches(':focus')) {
                            return
                        }
                        e.preventDefault()
                    }}
                    onBlur={(e) => {
                        e.currentTarget.readOnly = true
                        const newValue = e.target.value.trim()
                        if (!newValue) {
                            e.target.value = scene.id
                            return
                        }
                        setScenes((draft) => {
                            draft[index].displayName = newValue
                        })
                    }}
                />
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                        e.preventDefault()
                        deleteScene(scene.id)
                    }}
                    className="justify-center items-center"
                >
                    <Trash2 className="h-4 w-4 text-destructive" />
                </Button>
            </div>
        </div>
    )
}



const ScenesEditor = () => {
    const { scenes, setScenes, viewMode } = useCampaignEditor()
    const { flows } = useCampaignEditor()
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
    const [sceneToDelete, setSceneToDelete] = useState<string | null>(null)

    const [currentSceneIndex, setCurrentSceneIndex] = useAtom(currentSceneIndexAtom)
    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)

    const currentScene = useMemo(() => scenes[currentSceneIndex], [currentSceneIndex, scenes])

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    )

    const handleDragEnd = (event: { active: any; over: any }) => {
        const { active, over } = event

        if (active.id !== over.id) {
            setScenes((draft) => {
                const oldIndex = draft.findIndex((scene) => scene.id === active.id)
                const newIndex = draft.findIndex((scene) => scene.id === over.id)

                // Swap scenes
                const reorderedScenes = arrayMove(draft, oldIndex, newIndex)

                // Update current scene index if the current scene was moved
                if (currentScene.id === active.id) {
                    setCurrentSceneIndex(newIndex)
                }

                return reorderedScenes
            })
        }
    }

    const deleteScene = (id: string) => {
        console.log('Deleting scene: ', id)
        // Check if the scene is used in any flow nodes
        const sceneUsedInFlow = flows.flatMap((f) => f.nodes).some((node) => node.payload?.['sceneId'] === id)

        if (sceneUsedInFlow) {
            setSceneToDelete(id)
            setDeleteDialogOpen(true)
            return
        }

        // If scene is not used in flow, delete directly
        setScenes((draft) => {
            const index = draft.findIndex((screen) => screen.id === id)
            if (index !== -1) {
                draft.splice(index, 1)
            }
        })
    }

    const handleConfirmDelete = () => {
        console.log('Delete: ', sceneToDelete)
        if (sceneToDelete) {
            setScenes((draft) => {
                console.log('Draft: ', draft)
                const index = draft.findIndex((screen) => screen.id === sceneToDelete)
                if (index !== -1) {
                    draft.splice(index, 1)
                }
            })
            setDeleteDialogOpen(false)
            setSceneToDelete(null)
        }
    }

    const handleSceneChange = (value: string) => {
        if (value.length == 0) {
            return
        }
        const changeTo = scenes.findIndex((s) => s.id == value)
        const newSceneIndex = changeTo >= 0 ? changeTo : 0
        setCurrentSceneIndex(newSceneIndex)

        // Clear widget selection when switching pages and automatically select root widget
        const newScene = scenes[newSceneIndex]
        if (newScene?.rootWidget) {
            setEditorSelection({
                type: 'ui-widget',
                widgetId: newScene.rootWidget.id,
                time: Date.now(),
            })
        } else {
            // If no root widget, clear selection completely
            setEditorSelection(null)
        }
    }

    return (
        <>
            <SceneEditor currentSceneId={currentScene?.id}>
                <div className="h-full flex flex-row space-x-2">
                    {/* Left Column - Fixed width */}
                    <div className="w-[380px] flex-none bg-card text-foreground pt-3 ps-4 pe-2 space-y-4 rounded-sm overflow-auto">
                        <div className="flex flex-col">
                            {currentScene?.type === CampaignSceneType.WidgetTemplateEditor ? (
                                <div className="flex items-center justify-between border-b pb-2 mb-2">
                                    <div className="font-bold text-sm">Template: {currentScene?.displayName || 'Widget Template'}</div>
                                </div>
                            ) : (
                                <div className="flex items-center justify-between border-b pb-2 mb-2">
                                    <div className="font-bold text-sm">Pages</div>
                                    <AddPagePopover />
                                </div>
                            )}

                            {currentScene?.type !== CampaignSceneType.WidgetTemplateEditor && (
                                <DndContext modifiers={[restrictToVerticalAxis]} sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                                    <SortableContext items={scenes.map((scene) => scene.id)} strategy={rectSortingStrategy}>
                                        <div className="h-[240px] overflow-auto scroll-sm">
                                            <div className="flex w-full flex-col flex-grow items-start gap-1">
                                                {scenes.map((scene, index) => {
                                                    const isSelected = scene.id === currentScene?.id
                                                    return (
                                                        <SortablePage key={scene.id} scene={scene} index={index} isSelected={isSelected} handleSceneChange={handleSceneChange} deleteScene={deleteScene} />
                                                    )
                                                })}
                                            </div>
                                        </div>
                                    </SortableContext>
                                </DndContext>
                            )}

                            {currentScene && <SceneEditor.WidgetTree />}
                        </div>
                    </div>

                    {currentScene && <SceneEditor.EditorView />}

                    {/* Right Column - Fixed width */}
                    <div className="w-[420px] flex-none bg-card text-foreground p-4 space-y-4 rounded-sm overflow-auto scroll-sm">
                        <SceneEditor.WidgetSettings  />
                    </div>
                </div>
            </SceneEditor>
            <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>This may lead to unexpected behavior.</DialogTitle>
                        <DialogDescription>
                            Warning: This page is used in the flow editor. Deleting it may break the campaign flow and lead to unexpected behavior. You'd have to edit the logic of the campaign after
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleConfirmDelete}>
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}

const AddPagePopover = () => {
    const { scenes, setScenes } = useCampaignEditor()

    const addScene = () => {
        console.log('Add scene')
        let newScreen: CampaignScene = {
            id: createShortUuid(),
            displayName: `Page ${scenes.length + 1}`,
            type: CampaignSceneType.Page,
            rootWidget: {
                id: createShortUuid(),
                componentName: 'BasicLayoutWidget',
                settings: {},
                children: [],
            },
        }

        setScenes([newScreen, ...scenes])
    }

    const pageTemplates = [
        {
            id: 'empty',
            title: 'Empty Page',
            description: 'Start with a clean slate',
            preview: (
                <div className="w-full aspect-[1.6] bg-white rounded-lg shadow-sm border flex items-center justify-center">
                    <div className="text-center p-4">
                        <h3 className=" text-sm mb-1">Ship sites with style.</h3>
                        <div className="bg-slate-100 w-16 h-2 mx-auto rounded"></div>
                    </div>
                </div>
            ),
        },
        {
            id: 'split',
            preview: (
                <div className="w-full aspect-[1.6] bg-white rounded-lg shadow-sm border overflow-hidden">
                    <div className="flex h-full">
                        <div className="w-1/2 bg-blue-50 flex items-center justify-center">
                            <div className="text-center p-4">
                                <h3 className="font-semibold text-sm mb-1">Ship sites with style.</h3>
                                <div className="bg-blue-200 w-16 h-2 mx-auto rounded"></div>
                            </div>
                        </div>
                        <div className="w-1/2 flex items-center justify-center">
                            <div className="bg-slate-100 w-16 h-16 rounded"></div>
                        </div>
                    </div>
                </div>
            ),
        },
        {
            id: 'hero',
            preview: (
                <div className="w-full aspect-[1.6] bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg shadow-sm border flex items-center justify-center">
                    <div className="text-center p-4">
                        <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4">
                            <h3 className="font-semibold text-sm mb-1">Add Sections or Complete Pages</h3>
                            <div className="bg-slate-200 w-20 h-2 mx-auto rounded mt-2"></div>
                        </div>
                    </div>
                </div>
            ),
        },
        {
            id: 'split',
            preview: (
                <div className="w-full aspect-[1.6] bg-white rounded-lg shadow-sm border overflow-hidden">
                    <div className="flex h-full">
                        <div className="w-1/2 bg-blue-50 flex items-center justify-center">
                            <div className="text-center p-4">
                                <h3 className="font-semibold text-sm mb-1">Ship sites with style.</h3>
                                <div className="bg-blue-200 w-16 h-2 mx-auto rounded"></div>
                            </div>
                        </div>
                        <div className="w-1/2 flex items-center justify-center">
                            <div className="bg-slate-100 w-16 h-16 rounded"></div>
                        </div>
                    </div>
                </div>
            ),
        },
        {
            id: 'split',
            preview: (
                <div className="w-full aspect-[1.6] bg-white rounded-lg shadow-sm border overflow-hidden">
                    <div className="flex h-full">
                        <div className="w-1/2 bg-blue-50 flex items-center justify-center">
                            <div className="text-center p-4">
                                <h3 className="font-semibold text-sm mb-1">Ship sites with style.</h3>
                                <div className="bg-blue-200 w-16 h-2 mx-auto rounded"></div>
                            </div>
                        </div>
                        <div className="w-1/2 flex items-center justify-center">
                            <div className="bg-slate-100 w-16 h-16 rounded"></div>
                        </div>
                    </div>
                </div>
            ),
        },
    ]

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="w-8 h-8 hover:bg-accent/50">
                    <Plus className="w-5 h-5" strokeWidth={2} />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[400px] p-4 ms-4  border-0 rounded-lg overflow-hidden" align="start" sideOffset={-46} alignOffset={30}>
                <div className="space-y-4 max-h-[1000px] overflow-auto pe-3 scroll-sm">
                    <div>
                        <h3 className="font-semibold mb-1">Add new page</h3>
                        <p className="text-sm text-muted-foreground">Select a template to get started</p>
                    </div>
                    <div className="grid gap-4 ">
                        {pageTemplates.map((template) => (
                            <Button key={template.id} variant="ghost" className="h-auto p-0 hover:bg-transparent " onClick={() => addScene()}>
                                <div className="w-full text-left">
                                    <div className="mb-3 text-muted-foreground">{template.preview}</div>
                                </div>
                            </Button>
                        ))}
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    )
}

function LogoPopover() {
    const { currentOrganization } = useCurrentOrganization()
    const { campaignData } = useCampaignData()

    const { exportCampaignToTemplate } = useCampaignTemplates()

    const makeTemplate = () => {
        const template = exportCampaignToTemplate(campaignData.campaign, 'My Campaign', ['test'])
        console.log('template', JSON.stringify(template))
    }

    return (
        <div className="flex items-center  ">
            <Popover>
                <PopoverTrigger asChild>
                    <Button variant="ghost" className=" bg-accent  px-3 py-5">
                        <div className="items-center rounded flex gap-1 flex-row text-accent-foreground">
                            <BotIcon className="h-4 w-4" />
                            <ChevronDown className="h-3 w-3" />
                        </div>
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-56 p-2" align="start">
                    <div className="grid gap-1">
                        <Link to={`/w/${currentOrganization?.shortId}`} className="flex items-center gap-2 rounded-sm px-2 py-1 text-sm hover:bg-accent">
                            <LayoutGrid className="size-4" />
                            Go to Dashboard
                        </Link>

                        <Link to={`#`} onClick={() => makeTemplate()} className="flex items-center gap-2 rounded-sm px-2 py-1 text-sm hover:bg-accent">
                            <ShareIcon className="size-4" />
                            Export to Template
                        </Link>
                    </div>
                </PopoverContent>
            </Popover>
        </div>
    )
}

function SaveStateBadge() {
    const { campaignData } = useCampaignData()
    const { campaignSaveState, saveChanges } = useCampaignEditor()
    const [newName, setNewName] = useState(campaignData?.campaign?.name || '')
    const [isOpen, setIsOpen] = useState(false)

    const handleNameChange = async () => {
        if (newName.trim() !== '') {
            campaignData.campaign.name = newName
            saveChanges(CampaignSaveMode.SaveAsDraft)
            setIsOpen(false)
        }
    }

    return (
        <div>
            <nav className="flex items-center space-x-2 text-sm ms-2">
                {campaignSaveState.label.length > 0 && (
                    <Badge>
                        {campaignSaveState.isLoading && <Loader2Icon className="w-4 h-4 me-1 animate-spin" />}
                        {campaignSaveState.label && <span>{campaignSaveState.label}</span>}
                    </Badge>
                )}
            </nav>
        </div>
    )
}

function MobileViewSwitcher() {
    const { viewMode, setViewMode } = useCampaignEditor()

    return (
        <div className="flex items-center gap-2 flex-grow justify-center mt-[-70px]">
            <TooltipProvider delayDuration={0}>
                <ToggleGroup type="single" size="sm" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'desktop' | 'mobile')} className="flex rounded-lg p-1 bg-secondary">
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <ToggleGroupItem value="desktop" aria-label="Desktop view" className="data-[state=on]:bg-background data-[state=on]:text-foreground">
                                <MonitorIcon className="w-4 h-4" />
                            </ToggleGroupItem>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Desktop view</p>
                        </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                        <TooltipTrigger asChild>
                            <ToggleGroupItem value="mobile" aria-label="Mobile view" className="data-[state=on]:bg-background data-[state=on]:text-foreground">
                                <Smartphone className="w-4 h-4" />
                            </ToggleGroupItem>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Mobile view</p>
                        </TooltipContent>
                    </Tooltip>
                </ToggleGroup>
            </TooltipProvider>
        </div>
    )
}

function Header() {
    const { campaignData } = useCampaignData()
    const { saveChanges } = useCampaignEditor()
    const { rewardSet, setRewardSet } = useCampaignRewardSet()

    const [isShareDialogOpen, setIsShareDialogOpen] = useState(false)
    const [isRewardsOpen, setIsRewardsOpen] = useState(false)
    const [isPublishing, setIsPublishing] = useState(false)
    // const [publishError, setPublishError] = useState<string | null>(null) // Optional: Track publishing error

    const performInitialPublish = async () => {
        if (campaignData.campaign.isPublished) {
            return
        }

        setIsPublishing(true)
        try {
            await saveChanges(CampaignSaveMode.SaveAndPublish)
            setIsShareDialogOpen(true)
        } catch (error) {
            console.error('Publish Error:', error)
            toast({
                title: 'Error',
                description: 'Failed to publish the campaign. Please try again.',
                variant: 'destructive',
            })
        } finally {
            setIsPublishing(false)
        }
    }


    return (
        <header className="flex items-center justify-between h-[70px] text-foreground">
            <div className="flex items-center gap-2">
                <LogoPopover />
                <TabsList>
                    <TabsTrigger value="design">Design</TabsTrigger>
                    <TabsTrigger value="logic">Logic</TabsTrigger>
                </TabsList>
                <SaveStateBadge />
            </div>

            <div className="flex items-center space-x-[1px] pe-5">
                <div className="flex flex-row items-center justify-center">
                    <OrganizationMembers organizationId={campaignData?.campaign?.workspaceId}>
                        <DialogTrigger>
                            <div className="flex gap-4 items-center me-4">
                                <OrganizationMembers.Avatars />
                            </div>
                        </DialogTrigger>

                        <OrganizationMembers.Dialog />
                    </OrganizationMembers>
                </div>

                    <TooltipProvider delayDuration={0}>

                <Tooltip>
                            <TooltipTrigger>
                                        <Button variant="ghost" size="icon" className="text-foreground hover:text-foreground hover:bg-accent/30" onClick={() => setIsRewardsOpen(true)}>
                                            <TrophyIcon className="w-4 h-4" strokeWidth={2} />
                                        </Button>
                                        <RewardSetDialog open={isRewardsOpen} onOpenChange={setIsRewardsOpen} set={rewardSet as any} onChange={(next) => setRewardSet(next as any)} />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Rewards</p>
                            </TooltipContent>


                        </Tooltip>

                    <Tooltip>
                        <Tooltip>
                            <TooltipTrigger>
                                <Dialog>
                                    <DialogTrigger asChild>
                                        <Button variant="ghost" size="icon" className="text-foreground hover:text-foreground hover:bg-accent/30">
                                            <TicketIcon className="w-4 h-4" strokeWidth={2} />
                                        </Button>
                                    </DialogTrigger>
                                    <DialogContent className="min-w-[800px] h-[830px] overflow-hidden ">
                                        <div className="">
                                            <DialogHeader className="flex flex-grow-0">
                                                <DialogTitle>Coupons Manager</DialogTitle>
                                            </DialogHeader>
                                            <CreateNewCouponPack campaignId={campaignData?.campaign?.id} />
                                        </div>
                                    </DialogContent>
                                </Dialog>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Coupons Manager</p>
                            </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                            <TooltipTrigger>
                                <SettingsDialog defaultTab="general">
                                    <Button variant="ghost" size="icon" className="text-foreground hover:text-foreground hover:bg-accent/30">
                                        <SettingsIcon className="w-4 h-4" strokeWidth={2} />
                                    </Button>
                                </SettingsDialog>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Settings</p>
                            </TooltipContent>
                        </Tooltip>



                        <Tooltip>
                            <TooltipTrigger>
                                <AssetManagerDialog organizationId={campaignData?.campaign?.workspaceId}>
                                    <Button variant="ghost" size="icon" className="text-foreground hover:text-foreground hover:bg-accent/30">
                                        <ImagesIcon className="w-4 h-4" strokeWidth={2} />
                                    </Button>
                                </AssetManagerDialog>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Asset Manager</p>
                            </TooltipContent>
                        </Tooltip>

                        <TooltipContent>
                            <p>Share</p>
                        </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                        <TooltipTrigger>
                            <IntegrationsDialog>
                                <Button variant="ghost" size="icon" className="text-foreground hover:text-foreground hover:bg-accent/30">
                                    <ZapIcon className="w-4 h-4" strokeWidth={2} />
                                </Button>
                            </IntegrationsDialog>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Integrations</p>
                        </TooltipContent>
                    </Tooltip>

                    <div className="ps-4"></div>

                    <Popover>
                        <PopoverTrigger asChild>
                            <Button size="sm" className="flex items-center space-x-2 px-4 " onClick={() => performInitialPublish()}>
                                {isPublishing ? (
                                    <>
                                        <Loader2 className="w-4 h-4 animate-spin" />
                                        <span className="font-medium">Publishing...</span>
                                    </>
                                ) : (
                                    <span className="font-medium">Publish</span>
                                )}
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[400px] p-0 mt-2" align="end">
                            <ShareCampaignDialog isOpen={isShareDialogOpen} setIsOpen={setIsShareDialogOpen} campaignId={campaignData?.campaign?.id}></ShareCampaignDialog>
                        </PopoverContent>
                    </Popover>
                </TooltipProvider>
            </div>
        </header>
    )
}

const DomainsTab = () => {
    const { currentOrganization } = useCurrentOrganization()
    const { campaignDomainSettings, data, setCampaignDomainSettings, saveChanges } = useCampaignEditor()
    const { toast } = useToast()

    const { data: activeDomains = [] } = useFetch<CustomDomainDto[]>(currentOrganization?.id ? `organizations/${currentOrganization.id}/domains/active` : null)

    const getShareUrl = () => {
        const selectedDomainName = activeDomains.find((domain) => domain.id === campaignDomainSettings?.customDomainId)?.domainName
        if (campaignDomainSettings?.customDomainId) {
            return campaignDomainSettings?.slug ? `https://${selectedDomainName}/${campaignDomainSettings?.slug}` : `https://${selectedDomainName}`
        }
        return `https://play.beakbyte.com/${data.id}`
    }

    const handleSave = async () => {
        try {
            await saveChanges(CampaignSaveMode.SaveAsDraft)
            toast({
                title: 'Success',
                description: 'Domain settings saved successfully',
            })
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to save domain settings',
                variant: 'destructive',
            })
        }
    }

    return (
        <div className="space-y-6">
            <div>
                <h3 className="text-lg font-medium">Domain Settings</h3>
                <p className="text-sm text-muted-foreground">Configure the domain settings for your campaign.</p>
            </div>

            <DomainSettings />

            <Code text={getShareUrl()} />

            <div className="flex justify-end pt-4">
                <Button onClick={handleSave} className="w-[120px]">
                    Save
                </Button>
            </div>
        </div>
    )
}

const GeneralTab = () => {
    const { data, saveChanges } = useCampaignEditor()
    const { toast } = useToast()
    const [formData, setFormData] = useState({
        name: data?.name || '',
    })

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }))
    }

    const handleSave = async () => {
        try {
            // Update campaign data with new values
            data.name = formData.name

            await saveChanges(CampaignSaveMode.SaveAsDraft)
            toast({
                title: 'Success',
                description: 'General settings saved successfully',
            })
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to save general settings',
                variant: 'destructive',
            })
        }
    }

    return (
        <div className="space-y-6">
            <div>
                <h3 className="text-lg font-medium">General Settings</h3>
                <p className="text-sm text-muted-foreground">Configure general settings for your campaign.</p>
            </div>
            <div className="space-y-4">
                <div className="space-y-2">
                    <Label>Campaign Name</Label>
                    <Input name="name" value={formData.name} onChange={handleInputChange} placeholder="Enter campaign name" />
                </div>
            </div>
            <div className="flex justify-end pt-4">
                <Button onClick={handleSave} className="w-[120px]">
                    Save
                </Button>
            </div>
        </div>
    )
}

export const SettingsDialog = ({
    children,
    isOpen,
    setIsOpen,
    defaultTab,
}: {
    children: React.ReactNode
    isOpen?: boolean
    setIsOpen?: React.Dispatch<React.SetStateAction<boolean>>
    defaultTab?: 'general' | 'domains' | 'leaderboards'
}) => {
    const [dialogOpen, setDialogOpen] = useState(false)

    // Use controlled or uncontrolled state
    const isControlled = isOpen !== undefined
    const open = isControlled ? isOpen : dialogOpen
    const onOpenChange = isControlled ? setIsOpen : setDialogOpen

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogTrigger asChild>{children}</DialogTrigger>
            <DialogContent className="sm:max-w-[800px] w-full h-[600px] p-0">
                <Tabs defaultValue={defaultTab} className="flex h-full">
                    {/* Sidebar with tabs */}
                    <div className="w-[200px] border-r border-border">
                        <TabsList className="flex flex-col h-full w-full rounded-none space-y-1 bg-secondary/30 0 p-2">
                            <TabsTrigger value="general" className="justify-start w-full px-3 py-2">
                                <Settings className="h-4 w-4 mr-2" />
                                General
                            </TabsTrigger>
                            <TabsTrigger value="domains" className="justify-start w-full px-3 py-2">
                                <Globe className="h-4 w-4 mr-2" />
                                Domains
                            </TabsTrigger>
                            <TabsTrigger value="leaderboards" className="justify-start w-full px-3 py-2">
                                <TrophyIcon className="h-4 w-4 mr-2" />
                                Leaderboards
                            </TabsTrigger>
                        </TabsList>
                    </div>

                    {/* Content area */}
                    <div className="flex-1 p-6">
                        <TabsContent value="general" className="mt-0 border-0 p-0">
                            <GeneralTab />
                        </TabsContent>

                        <TabsContent value="domains" className="mt-0 border-0 p-0">
                            <DomainsTab />
                        </TabsContent>

                        <TabsContent value="leaderboards" className="mt-0 border-0 p-0">
                            <LeaderboardsTab />
                        </TabsContent>
                    </div>
                </Tabs>
            </DialogContent>
        </Dialog>
    )
}

const IntegrationsDialog = ({ children }: { children: React.ReactNode }) => {
    const [open, setOpen] = useState(false)
    const { currentOrganization } = useCurrentOrganization()
    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>{children}</DialogTrigger>
            <DialogContent className="sm:max-w-[1150px] w-full">
                <DialogTitle>
                    {' '}
                    <h2 className="text-xl font-bold">Integrations</h2>
                </DialogTitle>
                <div className=" min-h-[300px] max-h-[800px] overflow-y-auto scroll-sm px-2">
                    <IntegrationsManager organizationId={currentOrganization?.id} />
                </div>
            </DialogContent>
        </Dialog>
    )
}

export default CampaignEditor
