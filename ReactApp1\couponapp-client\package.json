{"private": true, "scripts": {"build": "turbo build || node -e \"process.exit(0)\"", "dev": "turbo dev --concurrency 15", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "backend": "dotnet run --project ./../CouponApp.Server/CouponApp.Server.csproj"}, "devDependencies": {"@vitejs/plugin-basic-ssl": "^1.2.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "postcss": "^8.4.49", "prettier": "^3.2.5", "tailwindcss": "^3.4.15", "turbo": "^2.2.3", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-ngrok": "^1.1.2"}, "name": "with-vite", "packageManager": "npm@10.8.2", "workspaces": ["apps/*", "packages/*", "games/*"]}