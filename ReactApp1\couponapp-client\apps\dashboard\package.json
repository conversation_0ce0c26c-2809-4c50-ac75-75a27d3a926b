{"name": "couponapp-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build || node -e \"process.exit(0)\"", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^8.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.0", "@logto/react": "^3.0.15", "@nangohq/frontend": "^0.42.6", "@pixi/react": "^7.1.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@rajesh896/broprint.js": "^2.1.1", "@rive-app/react-canvas": "^4.13.7", "@uidotdev/usehooks": "^2.4.1", "@xyflow/react": "^12.3.0", "d3-hierarchy": "^3.1.2", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "diff": "^7.0.0", "difflib": "^0.2.4", "dnd-kit-sortable-tree": "^0.1.73", "framer-motion": "^11.3.17", "geist": "^1.3.1", "immer": "^10.1.1", "liquidjs": "^10.17.0", "lodash": "^4.17.21", "lucide-react": "^0.400.0", "non.geist": "^1.0.4", "re-resizable": "^6.9.17", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-contenteditable": "^3.3.7", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-fontpicker-ts": "^1.0.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.52.2", "react-mentions": "^4.4.10", "react-moveable": "^0.56.0", "react-qr-code": "^2.0.15", "react-resizable": "^3.0.5", "react-responsive": "^10.0.0", "react-rnd": "^10.4.11", "react-router-dom": "6.21.0", "react-slugify": "^4.0.1", "react-swipeable": "^7.0.1", "swr": "^2.2.5", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "twind": "^0.16.19", "use-immer": "^0.10.0", "use-undo": "^1.1.1", "usehooks-ts": "^3.1.0", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.25.9", "@shopify/prettier-plugin-liquid": "^1.5.2", "@types/node": "^20.14.9", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@welldone-software/why-did-you-render": "^8.0.3", "autoprefixer": "^10.4.19", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.14", "postcss": "^8.4.39", "prettier": "^3.3.3", "tailwindcss": "^3.4.4", "typescript": "^5.2.2", "vite": "^5.0.8"}}