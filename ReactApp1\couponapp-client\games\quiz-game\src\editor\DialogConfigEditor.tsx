import { useState } from 'react'
import { GameEditorComponentProps } from '@repo/shared/lib/game/game'
import { Button } from '@repo/shared/components/ui/button'
import { QuestionAnswerManagerEditor } from './components/QuestionAnswerManagerEditor'
import { TimerAndLivesEditor } from './components/TimerAndLivesEditor'
import { StartScreenSettingsEditor } from './components/StartScreenSettingsEditor'
import { ReactGameConfig } from '../types/config'
import { RewardsSettingsEditor } from '@repo/shared-game-utils/editor-components/RewardsSettingsEditor'
import { SoundSettingsEditor } from '@repo/shared-game-utils/editor-components/SoundSettingsEditor'

type CategoryType = 'questions-answers' | 'game-settings' | 'start-screen' | 'rewards' | 'sound-settings'

interface Category {
    id: CategoryType
    label: string
    description: string
}

const categories: Category[] = [
    {
        id: 'questions-answers',
        label: 'Questions & Answers',
        description: 'Manage quiz questions and their answers'
    },
    {
        id: 'game-settings',
        label: 'Timer & Lives',
        description: 'Configure timer and lives system'
    },
    {
        id: 'start-screen',
        label: 'Start Screen',
        description: 'Configure welcome screen settings'
    },
    {
        id: 'rewards',
        label: 'Rewards',
        description: 'Configure the rewards system'
    },
    {
        id: 'sound-settings',
        label: 'Sound Settings',
        description: 'Configure sound effects and music'
    }
]

export default function DialogConfigEditor({ config, updateConfig, widgetId }: GameEditorComponentProps) {
    if (!config) config = {}

    const [selectedCategory, setSelectedCategory] = useState<CategoryType>('questions-answers')

    const handleConfigChange = (updates: any) => {
        updateConfig({ ...config, ...updates })
    }

    const renderCategoryContent = () => {
        switch (selectedCategory) {
            case 'questions-answers':
                return (
                    <QuestionAnswerManagerEditor
                        config={config}
                        configKey="questionAnswerSettings"
                        onChange={handleConfigChange}
                    />
                )
            case 'game-settings':
                return (
                    <TimerAndLivesEditor
                        config={config}
                        onChange={handleConfigChange}
                    />
                )
            case 'start-screen':
                return (
                    <StartScreenSettingsEditor
                        config={config}
                        onChange={handleConfigChange}
                    />
                )
            case 'rewards':
                return (
                    <RewardsSettingsEditor
                        config={config}
                        onChange={handleConfigChange}
                    />
                )
            case 'sound-settings':
                return (
                    <SoundSettingsEditor
                        config={config}
                        onChange={handleConfigChange}
                    />
                )
            default:
                return null
        }
    }

    return (
        <div className="flex h-[600px] gap-4">
            {/* Left Panel - Categories */}
            <div className="w-80 border-r pr-4">
                <div className="space-y-2">
                    {categories.map((category) => (
                        <Button
                            key={category.id}
                            variant={selectedCategory === category.id ? 'default' : 'ghost'}
                            className="w-full justify-start text-left h-auto p-3"
                            onClick={() => setSelectedCategory(category.id)}
                        >
                            <div>
                                <div className="font-medium">{category.label}</div>
                                <div className="text-xs text-muted-foreground mt-1">
                                    {category.description}
                                </div>
                            </div>
                        </Button>
                    ))}
                </div>
            </div>

            {/* Right Panel - Category Content */}
            <div className="flex-1">
                <div className="p-6">
                    <div className="mb-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="font-semibold text-lg">
                                    {categories.find(c => c.id === selectedCategory)?.label}
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                    {categories.find(c => c.id === selectedCategory)?.description}
                                </p>
                            </div>
                        </div>
                    </div>
                    {renderCategoryContent()}
                </div>
            </div>
        </div>
    )
}
