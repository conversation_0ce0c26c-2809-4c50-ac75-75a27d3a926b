import React from 'react'
import { registerNode } from '../../registry'
import type { NodeDefinition, NodeEditorBodyProps, NodeEditorPropertiesProps } from '../../types'
import type { Settings } from './node.types'
import { run } from './node.runtime'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Input } from '@repo/shared/components/ui/input'
import { useCampaignData } from '@repo/shared/lib/hooks/useCampaignStore'

function SetGameSceneNodeBody(_: NodeEditorBodyProps) {
    return <div></div>
}

function SetGameSceneProperties({ settings, onChange }: NodeEditorPropertiesProps<Settings>) {
    const { campaignData } = useCampaignData()

    return (
        <div className="space-y-2">
           
            <div className="space-y-1">
                <Label>Game scene</Label>
                <Input value={settings?.sceneId ?? ''} onChange={(e) => onChange({ sceneId: e.target.value as any })} placeholder="e.g. main, start, custom/xyz" />
            </div>
        </div>
    )
}

const def: NodeDefinition<Settings> = {
    type: 'client:SetGameScene',
    label: 'Set Game Scene',
    icon: 'GamepadIcon',
    inputs: () => [{ key: 'input' }],
    outputs: () => [{ key: 'output',  kind: 'event' }],
    editor: {
        renderNodeBody: SetGameSceneNodeBody,
        renderProperties: SetGameSceneProperties,
        onCreate: () => ({ settings: { widgetId: '', sceneId: '' } }),
        onDelete: () => {},
    },
    runtime: { run },
}

registerNode(def)
export default def

