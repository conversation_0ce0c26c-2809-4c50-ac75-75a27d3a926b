import React from 'react'
import { registerNode } from '../../registry'
import type { NodeDefinition, NodeEditorBodyProps, NodeEditorPropertiesProps } from '../../types'
import type { Settings } from './node.types'
import { run } from './node.runtime'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { useCampaignData } from '@repo/shared/lib/hooks/useCampaignStore'

function SetSceneNodeBody(_: NodeEditorBodyProps) {
	return <div></div>
}

function SetSceneProperties({ settings, onChange }: NodeEditorPropertiesProps<Settings>) {
	const { campaignData } = useCampaignData()
	const scenes = campaignData?.campaign?.config?.scenes ?? []

	return (
		<div className="space-y-1">
			<Label>Page</Label>
			<Select value={settings?.sceneId ?? ''} onValueChange={(v) => onChange({ sceneId: v })}>
				<SelectTrigger>
					<SelectValue placeholder="Select page..." />
				</SelectTrigger>
				<SelectContent>
					{scenes.map((s) => (
						<SelectItem key={s.id} value={s.id}>
							{s.displayName ?? s.id}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
		</div>
	)
}

const def: NodeDefinition<Settings> = {
	type: 'client:SetScene',
	label: 'Set Page',
	icon: 'SceneIcon',
    inputs: () => [{ key: 'input' }],
    outputs: () => [{ key: 'output',  kind: 'event' }],
	editor: {
		renderNodeBody: SetSceneNodeBody,
		renderProperties: SetSceneProperties,
		onCreate: () => ({ settings: { sceneId: '' } }),
		onDelete: () => {},
	},
	runtime: { run },
}

registerNode(def)
export default def


