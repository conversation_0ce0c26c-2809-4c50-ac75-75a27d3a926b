import React, { useMemo, useState } from 'react'
import { registerNode } from '../../registry'
import type { NodeDefinition, NodeEditorBodyProps, NodeEditorPropertiesProps, NodeEditorContext, GameScreenDefinition } from '../../types'
import type { Settings } from './node.types'
import { run } from './node.runtime'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Button } from '@repo/shared/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/shared/components/ui/dialog'

import { Plus, Edit } from 'lucide-react'
import { useGameRewardsDetails, RewardSet } from '@repo/shared/lib/game/useGameRewardsDetails'
import RewardSetDialog from '@repo/shared/features/rewards/components/RewardSetDialog'
import { ScrollArea } from '@repo/shared/components/ui/scroll-area'
import type { Reward } from '@repo/shared/lib/game/useGameRewardsDetails'

function RewardScreenNodeBody(_: NodeEditorBodyProps) {
	return <div className="w-[260px]"></div>
}

function RewardScreenProperties({ settings, onChange }: NodeEditorPropertiesProps<Settings>) {
	const [isDialogOpen, setIsDialogOpen] = useState(false)
	const [isCreatingNew, setIsCreatingNew] = useState(false)

	// For now, use a default widget ID. In the future, this should come from flow context
	const gameWidgetId = 'default'
	const { rewardSet, setRewardSet } = useGameRewardsDetails(gameWidgetId)

	// Single campaign reward set
	const campaignRewardSet = rewardSet
	const selectedReward: Reward | undefined = useMemo(
		() => campaignRewardSet?.rewards.find((r) => r.id === settings?.rewardId),
		[campaignRewardSet, settings?.rewardId]
	)



	const handleRewardSetChange = (updatedRewardSet: RewardSet) => {
		setRewardSet(updatedRewardSet)
	}

	const handleOpenDialog = () => {
		setIsDialogOpen(true)
	}

	const handleRewardPick = (value: string) => {
		if (value === 'create-new') {
			setIsCreatingNew(true)
			setIsDialogOpen(true)
		} else {
			onChange({ rewardId: value })
		}
	}

	return (
		<div className="space-y-2">
			<div className="space-y-1">
				<Label>Reward</Label>
				<Select value={settings?.rewardId ?? ''} onValueChange={handleRewardPick}>
					<SelectTrigger>
						<SelectValue placeholder="Select reward" />
					</SelectTrigger>
					<SelectContent>
						{campaignRewardSet?.rewards.map((r) => (
							<SelectItem key={r.id} value={r.id}>
								{r.name}
							</SelectItem>
						))}
						<SelectItem value="create-new">
							<div className="flex items-center">
								<Plus className="w-4 h-4 mr-2" />
								Create reward
							</div>
						</SelectItem>
					</SelectContent>
				</Select>
				{selectedReward ? (
					<div className="text-xs text-muted-foreground mt-1">Selected: {selectedReward.name}</div>
				) : null}
			</div>
			<div className="pt-1">
				<Button
					variant="link"
					size="sm"
					className="px-0"
					onClick={handleOpenDialog}
				>
					<Edit className="w-4 h-4 mr-1" />
					Manage campaign rewards
				</Button>
			</div>

			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="max-w-4xl p-0 overflow-hidden [&>button]:hidden flex flex-col">
				<RewardSetDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} set={campaignRewardSet} onChange={handleRewardSetChange} />
			</DialogContent>
			</Dialog>
		</div>
	)
}

const def: NodeDefinition<Settings> = {
	type: 'client:RewardScreen',
	label: 'Game Reward Screen',
	icon: 'GiftIcon',
	inputs: () => [{ key: 'input', acceptsOnly: ['game-outcome'] }],
	outputs: () => [{ key: 'output', kind: 'event' }],
	exposeScreens: (_settings: Settings, ctx: NodeEditorContext): GameScreenDefinition[] => {
		const edge = ctx.inputEdges[0]
		const endLabel = (edge?.data as any)?.endLabel || 'Outcome'
		const screenId = "custom/node/" + edge.id
		const screens = [
			{
				id: screenId,
				name: "Outcome - " + endLabel,
			}
		]
		return screens
	},
	editor: {
		renderNodeBody: RewardScreenNodeBody,
		renderProperties: RewardScreenProperties,
	},
	runtime: { run },
}

registerNode(def)
export default def