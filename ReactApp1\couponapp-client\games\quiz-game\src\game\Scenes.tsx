import React, { useMemo, useRef } from 'react'

import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useAtom } from 'jotai'
import { gamePreviewScreenAtomFamily } from '@repo/shared/lib/atoms/editor-atoms'

import MainGame from './GameMain'
import { GameContainer } from '@repo/shared-game-utils/components/GameContainer'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { GameText } from '@repo/shared-game-utils/components/GameText'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { useMusic } from '@repo/shared-game-utils/hooks/useSounds'
import { UiWidgetContainer } from '@repo/shared-game-utils/components/UiWidgetContainer'

export const StartScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <GameContainer
                config={config.startScreenOverlay}
                dataConfigKey="startScreenOverlay"
                resolveAssetUrl={resolveAssetUrl}
                onClick={(e) => {
                    e.stopPropagation()
                }}
            >
                <GameText
                    config={config.startScreenTitle}
                    dataConfigKey="startScreenTitle"
                    className="mb-6"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />
                <GameButton
                    config={config.startScreenStartButton}
                    dataConfigKey="startScreenStartButton"
                    onClick={(e) => {
                        e.stopPropagation()
                        if (onButtonClick) {
                            onButtonClick()
                        }
                    }}
                />
            </GameContainer>
        </div>
    )
}



export const LoadingRewardScreen: React.FC = () => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()
    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <UiWidgetContainer widgetId={'loading-reward'} />
        </div>
    )
}




export const CustomScreen: React.FC<{ screenId?: string }> = ({ screenId }) => {
    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <UiWidgetContainer widgetId={screenId} />
        </div>
    )
}


export interface PreviewSceneProps {
    config: ReactGameConfig
    widgetId: string
    resolveAssetUrl: (id: AssetUrl) => string
    children?: any
}

export const PreviewScene: React.FC<PreviewSceneProps> = ({ config, widgetId, resolveAssetUrl, children }) => {
    const [selectedScreen] = useAtom(gamePreviewScreenAtomFamily(widgetId))
    const initialGameScreenChecked = useRef(false)


    return (
        <MainGame
            config={config}
            widgetId={widgetId}
            isPreview={true}
            resolveAssetUrl={resolveAssetUrl}
            currentScreenId={selectedScreen}
            initialGameScreenChecked={initialGameScreenChecked}
            children={children}
            setCurrentScreenId={() => { }}
            defaultConfig={defaultGameConfig}
        />
    )
}
