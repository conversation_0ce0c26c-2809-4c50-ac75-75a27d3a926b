import { GlobalVariable } from "../hooks/useGlobalVariables";
import { CampaignFlowNode, CampaignFlow } from "../campaign/actionNode";
import { Widget } from "./editor";
import { ProjectMiniDto } from "./project";
import { FlowGraph } from "../flow";

export interface CreateCampaignDto {
  name: string;
  organizationId: string;
  // projectId: string;
  config: CampaignConfig;
}

export interface CampaignMiniDto {
  id: string;
  name: string;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string | null;
  projectId: string;
}

export interface PublicCampaignDto {
  id: string;
  name: string;
  workspaceId: string;
  createdByUserId: string;
  config: CampaignConfig;
  createdAt?: string;
  isPublished?: boolean;
  assets?: CampaignAsset[];
}

export interface CampaignEditorDto {
  id: string;
  isPublished: boolean;
  config: CampaignConfig;
  createdAt: string;
  updatedAt: string | null;
  name: string;
  workspaceId: string;
  campaignDomainSettings?: CampaignDomainSettingsDto;
}

export interface CampaignUpdateDto {
  saveMode: CampaignSaveMode;
  config: CampaignConfig;
  name?: string;
  campaignDomainSettings?: CampaignDomainSettingsDto;
}

export interface CampaignDomainSettingsDto {
  customDomainId?: string;
  slug?: string;
}

export enum CampaignSaveMode {
  SaveAsDraft = 0,
  SaveAndPublish = 1,
}

export type CampaignData = {
  campaign: CampaignEditorDto;
  hasParticipated: boolean;
  googleAnalyticsMeasurementId?: string;
  metaPixelId?: string;
  tiktokAdsPixelId?: string;
  assets?: CampaignAsset[];
};

export enum CampaignSceneType {
  MainPage = "main_page",
  Page = "page",
  LeadFormPage = "lead_form_page",
  GamePage = "game_page",
  EndPage = "end_page",
  WidgetTemplateEditor = "widget_template_editor",
}

export interface CampaignScene {
  rootWidget: Widget;
  id: string;
  displayName?: string;
  type?: CampaignSceneType;
}

export interface CampaignPublicDto {
  id: string;
  name: string;
  config: CampaignConfig;
  createdAt?: string;
  isPublished?: boolean;
}

export interface CampaignConfig {
  revision: number;
  scenes: CampaignScene[];
  flows: FlowGraph[];
  variables: GlobalVariable[];
  usedAssetIds: string[];
}

export interface CampaignAsset {
  id: string;
  url: string;
}
