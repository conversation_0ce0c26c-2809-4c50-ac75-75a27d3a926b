// vite.config.ts
import { fileURLToPath, URL } from "node:url";
import plugin from "file:///C:/Users/<USER>/RiderProjects/ReactIdentity/ReactApp1/couponapp-client/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { defineConfig } from "file:///C:/Users/<USER>/RiderProjects/ReactIdentity/ReactApp1/couponapp-client/node_modules/vite/dist/node/index.js";
import mkcert from "file:///C:/Users/<USER>/RiderProjects/ReactIdentity/ReactApp1/couponapp-client/node_modules/vite-plugin-mkcert/dist/mkcert.mjs";
var __vite_injected_original_import_meta_url = "file:///C:/Users/<USER>/RiderProjects/ReactIdentity/ReactApp1/couponapp-client/apps/dashboard/vite.config.ts";
var vite_config_default = defineConfig({
  plugins: [
    plugin({
      babel: {
        plugins: [["@babel/plugin-proposal-decorators", { version: "2023-11" }]]
      }
    }),
    mkcert()
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url)),
      "@games": fileURLToPath(new URL("../../games", __vite_injected_original_import_meta_url)),
      "@dashboard": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
    }
  },
  server: {
    https: true,
    proxy: {
      "^/api/": {
        target: "http://localhost:7249/",
        secure: false
      }
    },
    port: 5173,
    host: "0.0.0.0"
    // Allow access from all network interfaces
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
