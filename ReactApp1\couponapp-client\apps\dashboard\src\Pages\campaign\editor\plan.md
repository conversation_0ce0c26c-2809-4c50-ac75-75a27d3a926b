# Widget Template Editor — Minimal Plan

## Goal
Enable editing a single mock "Widget Template" inside the existing Campaign Editor, without new routes or complex infra. Keep it extremely simple and local to the editor.

## Constraints
- No new routes.
- Exactly one global mock template (no add/delete/list for templates).
- Left sidebar shows the template name instead of pages when in template mode.
- The widget tree root is the template root when in template mode.
- Center editor shows the template on a magenta background.
- Everything reuses current editor components as much as possible.

## UX Flow (Simple)
1. Mode switch in Campaign Editor header: [Campaign | Widget Template].
2. When "Widget Template" is active:
   - Sidebar header displays template name (e.g., "Template: Example Widget").
   - Pages list is hidden/disabled.
   - Widget tree shows nodes from the template (template root becomes tree root).
   - <PERSON><PERSON> renders the template on a magenta page background.
   - Properties panel edits selected template node as usual.
3. When switching back to Campaign mode, all current behavior remains unchanged.

## State (Minimal, Global)
- Create a small zustand store for template editor state (persist optional):
  - isEditingTemplate: boolean
  - template: WidgetTree (single root + children)
  - selectedNodeId: string | null
  - actions: setIsEditingTemplate, setTemplate, selectNode, updateNode
- Initialize `template` with a tiny mocked WidgetTree compatible with existing widget system (e.g., root container -> text/button).

## Integration Points
- CampaignEditor root:
  - Add the mode switch UI.
  - If isEditingTemplate === true, provide context/selectors that point to template store instead of campaign/page stores.
- Sidebar:
  - Conditionally render: template name header; hide pages list; show normal widget tree bound to template state.
- Canvas:
  - Render existing WidgetCanvas with data from template store.
  - Apply magenta background on the canvas/page (no other layout changes).
- Properties Panel:
  - Reuse existing controls; they operate on nodes from the template store when in template mode.
- Breadcrumbs:
  - Show the template name when in template mode; otherwise keep current behavior.

## Styling
- Editor background for template mode: solid magenta (e.g., #FF00FF). Keep dark-mode fit.

## Non-goals (Now)
- No template CRUD (no creating/deleting/renaming templates).
- No backend persistence or API.
- No routing changes.
- No advanced UI around templates (lists, galleries, etc.).

## Implementation Steps
1. Add zustand store `useWidgetTemplateStore` with minimal shape and a mocked `template`.
2. Add a simple mode toggle in CampaignEditor header and wire it to `isEditingTemplate`.
3. In CampaignEditor composition, branch by mode:
   - Provide template store/context to Sidebar, Canvas, Properties when in template mode.
4. Sidebar changes for template mode:
   - Replace pages area with a small header: Template name.
   - Feed WidgetTreeView with template nodes.
5. Canvas changes for template mode:
   - Render template root as the widget tree root.
   - Set magenta background.
6. Properties Panel: ensure it reads/writes via template store when in template mode.
7. Light smoke test manually: switch modes; ensure selection, tree, props, and canvas work with template data.

## Risks / Notes
- Ensure components accept a generic data source (campaign vs template) without duplicating code.
- Keep diffs minimal by using conditional providers or a simple flag prop.
- Persisting the template is optional; can be added later via zustand persist if needed.

