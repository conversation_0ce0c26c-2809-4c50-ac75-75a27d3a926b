# Widget Template Editor as a Page Type — Minimal Plan

## Goal
Use a special page type to show the Widget Template Editor with zero new routes or modes. When a page of this type is active, the editor renders exactly the template-UX you requested.

## Approach (Simplest)
- Introduce a page type: `widget-template-editor`.
- When the current page has this type, the editor switches its UI/data to the widget template editor view.
- Everything reuses existing editor pieces; no global mode toggle, no extra stores.

## Behavior when the page is displayed
- Sidebar
  - Header: `Template: <name>`
  - Hide/disable the pages list
  - Widget tree root = template root from the page data
- Canvas
  - Render the template tree
  - Page background = magenta (#FF00FF), dark‑mode friendly
- Properties panel
  - Edits the selected node from the template tree
- Breadcrumbs
  - Show the template name

## Data / State
- Extend Page shape to support the new type and embed the template tree:
  - `type: 'widget-template-editor'`
  - `name: string`
  - `templateTree: WidgetTree` (root + children)
- Use existing selection/updates, scoped to `currentPage.templateTree`.

## Implementation Steps
1. Add `PageType.WidgetTemplateEditor = 'widget-template-editor'` and TS typings.
2. In CampaignEditor, if `currentPage.type === 'widget-template-editor'`:
   - Provide the template tree to WidgetTreeView and Properties
   - Replace sidebar pages section with `Template: <name>` header
   - Render the canvas from `templateTree` and set magenta background
3. Provide a `DEFAULT_TEMPLATE_PAGE` with a tiny mock `templateTree` for initial testing.
4. Verify selection and property edits affect `templateTree` nodes.
5. Manual smoke test: switch to this page, confirm sidebar/canvas/properties behave as above.

## Non‑goals (Now)
- No template CRUD, no backend persistence, no routing changes.
- No galleries or advanced UX.

## Notes
- Keep diffs minimal; conditional rendering by page type only.
- Ensure colors read well in dark mode.
