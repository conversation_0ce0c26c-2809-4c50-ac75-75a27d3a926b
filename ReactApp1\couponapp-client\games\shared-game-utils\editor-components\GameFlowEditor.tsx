import { Button } from '@repo/shared/components/ui/button'
import { Card, CardContent } from '@repo/shared/components/ui/card'
import { Label } from '@repo/shared/components/ui/label'
import { ArrowRight } from 'lucide-react'
import { createShortUuid } from '@repo/shared/lib/utils'
import { FlowGraph } from '@repo/shared/lib/flow/types'
import { useCampaignEditor } from '@dashboard/lib/hooks/useCampaignEditor'
import { useEventEmitter } from '@dashboard/lib/hooks/useEditorEventBus'

export interface EditorComponentProps {
    configKey: string
    config: any
    onChange: (changes: any) => void
    widgetId: string
}

export function GameFlowEditor({ configKey, config, onChange, widgetId }: EditorComponentProps) {
    const currentFlowId: string | null = (config?.[configKey] as any) ?? null

    const { flows, setFlows, setActiveTab, setSelectedFlowId } = useCampaignEditor()
    const emitter = useEventEmitter()

    const createAndOpenFlow = () => {
        const newId = createShortUuid()

        const startNode = {
            id: createShortUuid(),
            type: 'registryNode',
            position: { x: 100, y: 100 },
            data: { nodeType: 'trigger:OutcomeRouter' },
        }

        const newFlow: FlowGraph = { id: newId, name: "Game Flow", nodes: [startNode] as any, edges: [], parentWidgetId: widgetId }
        setFlows((draft) => {
            draft.push(newFlow as any)
        })

        onChange({ [configKey]: newId })
        console.log('newFlow', configKey, newFlow)
        console.log('config', config)
        emitter.emit('editor:flow_changed', {})
        emitter.emit('editor:show_flow', { flowId: newId })
        setActiveTab('logic')
        setSelectedFlowId(newId)
    }

    const openOrCreate = () => {
        if (!currentFlowId || !flows.find((f) => f.id === currentFlowId)) {
            createAndOpenFlow()
            return
        }

        const flow = flows.find((f) => f.id === currentFlowId)
        if (flow.parentWidgetId !== widgetId) {
            console.log('need to set parent widget id of flow.')
            setFlows((draft) => {
                const index = draft.findIndex((f) => f.id === currentFlowId)
                if (index !== -1) {
                    draft[index].parentWidgetId = widgetId
                }
            })
        }

        console.log("Flow: ", flow)
        emitter.emit('editor:show_flow', { flowId: currentFlowId })
        setActiveTab('logic')
        setSelectedFlowId(currentFlowId)
    }


    return (
        <div className="flex justify-center" data-editor-selectable-key={String(configKey)}>
           widget: {widgetId}

            <Button size="sm" variant="outline" onClick={openOrCreate} className="w-full">
                Edit Game Flow
            </Button>
        </div>
    )
}

