import type { NodeRuntimeContext, NodeRunResult, NodeChildRef } from '../../types'
import type { Settings } from './node.types'

export async function run({ ctx, settings, children }: { ctx: NodeRuntimeContext; settings: Settings; children?: NodeChildRef[] }): Promise<NodeRunResult> {
    console.log("Set game scene: ", settings.sceneId)
    if (settings?.sceneId ) {
        console.log('set-game-scene, lets just trigger game screen ', ctx)
        ctx.setGameScene(ctx.graph.parentWidgetId, settings.sceneId)
    }
    return { next: children }
}
