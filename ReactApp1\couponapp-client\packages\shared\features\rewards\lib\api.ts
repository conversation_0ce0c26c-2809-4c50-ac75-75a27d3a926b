import type { RewardDefinition, RewardSet } from '../types'
import { DEFAULTS as COUPON_REUSABLE_DEFAULTS } from '../reward-types/couponCodeReusable'

const MOCK_REWARD_SET = {
  id: 'default-set',
  rewards: [
    {
      id: 'reward-1',
      name: '50% Off Coupon',
      description: 'Get 50% off your next purchase',
      type: 'coupon-code-reusable' as const,
      image: { absoluteUrl: 'https://placehold.co/100x100/00ff00/ffffff?text=50OFF' },
      dropRate: 1.0,
      settings: { ...COUPON_REUSABLE_DEFAULTS, reusableCode: 'SAVE50' },
    },
  ] as RewardDefinition[],
} 

export async function fetchCampaignRewardSet(): Promise<RewardSet> {
  await delay(200)
  return MOCK_REWARD_SET as unknown as RewardSet
}

export async function fetchRewardById(rewardId?: string): Promise<RewardDefinition | null> {
  await delay(200)
  if (!rewardId) return null
  const all = MOCK_REWARD_SET.rewards
  return all.find((r) => r.id === rewardId) ?? null
}

function delay(ms: number) { return new Promise((r) => setTimeout(r, ms)) }

