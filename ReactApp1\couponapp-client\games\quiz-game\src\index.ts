import MainGame from './game/GameMain'
import { ConfigKeyEditor } from './editor/ConfigKeyEditor'
import MainConfigEditor from './editor/MainConfigEditor'
import { PreviewScene } from './game/Scenes'
import { defaultGameConfig, ReactGameConfig } from './types/config'
import { GameScreenId } from './types/screen'
import { GameModule, PreviewScreenDefinition } from '@repo/shared/lib/game/game'
import { registerValueSources } from '@repo/shared/lib/dynamic-values/registry'

// Preview screen definitions for the game
const previewScreens: PreviewScreenDefinition[] = [
    {
        screenId: 'start' as GameScreenId,
        displayName: 'Start Screen',
        visibleCheck: (config) => config.gameStartHandler?.enableStartScreen === true,
    },
    {
        screenId: 'main' as GameScreenId,
        displayName: 'Main Game Screen',
    },
    {
        screenId: 'custom/game-screen/loading-reward' as GameScreenId,
        displayName: 'Loading reward screen',
        customizable: true
    }
]


const QuizGame: GameModule = {
    id: 'quiz-game',
    name: 'Quiz Game',
    runtimeComponent: MainGame,
      // editorComponent: MainConfigEditor,
    //  configKeyEditor: ConfigKeyEditor,
    defaultConfig: defaultGameConfig,
    previewScene: PreviewScene, 
    previewScreens: previewScreens,
    configType: ReactGameConfig,
}

export default QuizGame

export { MainGame }


registerValueSources('quiz-game', [
  {
    path: "game/{widgetId}/score",
    type: "number",
    label: "Player Score",
    description: "Current player's game score - 1 correct answer is 1 point"
  },
  {
    path: "game/{widgetId}/currentQuestion",
    type: "number",
    label: "Current question",
    description: "The current question that is being displayed in game"
  },
  {
    path: "game/{widgetId}/reward",
    type: "reward-roll-result",
    label: "Reward roll result",
    description: "The result of the last reward roll. "
  }
])