import '@repo/shared/components/react-templates/widgets-loader-editor'
import './editor.css'

import { Widget, WidgetRenderingContext } from '@repo/shared/lib/types/editor'
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { WidgetRenderer } from './_components/WidgetRenderer'
import { CampaignDataProvider } from '@repo/shared/lib/hooks/useCampaignStore'
import { FontsLoader } from '@repo/shared/components/FontsLoader'
import { CampaignData } from '@repo/shared/lib/types/campaign'
import { GameAssetSelection } from '@repo/shared/lib/types/editorSelection'
import { CopyIcon, TrashIcon } from 'lucide-react'


import { createStore, getDefaultStore, Provider, useAtom, useAtomValue, useStore } from 'jotai'
import { currentSceneIndexAtom, gamePreviewScreenAtomFamily, selectedEditorItemAtom } from '@repo/shared/lib/atoms/editor-atoms'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { useWidgetSettings } from '@/lib/hooks/useWidgetSettings'
import { getGameModule } from '@repo/shared/lib/game/gameRegistry'
import { useFlowGameScreens } from '@repo/shared/lib/flow/hooks/useFlowGameScreens'
import { useCampaignData } from '@repo/shared/lib/hooks/useCampaignStore'
import { EditorProvider } from '@/lib/hooks/useEditor'
import { CampaignEditorProvider, useCampaignEditor } from '@/lib/hooks/useCampaignEditor'
import { DynamicValuesProvider } from '@repo/shared/lib/dynamic-values/provider'
import { getAllFlowGameScreens } from '@repo/shared/lib/flow/utils/graphAnalysis'
import { CampaignSceneType } from '@repo/shared/lib/types/campaign'




const useEditor = () => {
    //@ts-ignore
    const context: WidgetRenderingContext = window.context
    return context.editorContext.useEditorContext
}



const EditorFrame = () => {
    //@ts-ignore
    const context: WidgetRenderingContext = window.context

    //@ts-ignore
    const campaignData: CampaignData = window.campaignData

    //@ts-ignore
    const atomsStore = window.editorAtomsStore

       //@ts-ignore
    const campaignEditorContext = window.campaignEditorContext

    // Read current scene index from the shared Jotai store
    const [currentSceneIndex] = useAtom(currentSceneIndexAtom)
    const currentSceneType = campaignEditorContext?.scenes?.[currentSceneIndex]?.type

    const [renderCount, setRenderCount] = useState(0)
    const [hoveredWidgetId, setHoveredWidgetId] = useState<string | null>(null)

    //@ts-ignore
    window.update = () => {
        setRenderCount(renderCount + 1)
    }

    if (!context) {
        return <>Loading</>
    }

    return (
        <>
            {/* <CampaignEditorProvider campaignId={campaignData.campaign.id} > */}
                <CampaignDataProvider campaignData={campaignData}>
                    <DynamicValuesProvider>
                        <EditorProvider rootWidget={context.widget} setRootWidget={context.editorContext.useEditorContext.setRootWidget}>
                            <Provider store={atomsStore}>
                                <WidgetOverlays hoveredWidgetId={hoveredWidgetId} />

                                <div
                                    style={{
                                        width: '100%',
                                        height: '100vh',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        flexDirection: 'column',
                                        color: 'white',
                                        margin: 0,
                                        padding: 0,
                                        background: currentSceneType === CampaignSceneType.WidgetTemplateEditor ? '#FF00FF' : undefined,
                                    }}
                                >


                                    <FontsLoader scenes={campaignData?.campaign?.config?.scenes} />

                                    <CampaignDataProvider campaignData={campaignData}>
                                        <WidgetRenderer
                                            context={context}
                                            onClick={(w) => {
                                                context.editorContext.useEditorContext.selectWidget(w)
                                            }}
                                            onMouseMove={(w) => {
                                                setHoveredWidgetId(w?.id)
                                            }}
                                            onMouseLeave={() => {
                                                setHoveredWidgetId(null)
                                            }}
                                        />
                                    </CampaignDataProvider>
                                </div>
                            </Provider>
                        </EditorProvider>
                    </DynamicValuesProvider>
                </CampaignDataProvider>
            {/* </CampaignEditorProvider> */}
        </>
    )
}

const Toolbox = ({ widgetId }: { widgetId: string }) => {
    const { duplicateWidget, deleteWidget, rootWidget, findWidgetById } = useEditor()
    const selectedWidget = useMemo(() => findWidgetById(rootWidget, widgetId), [rootWidget, widgetId])


    const selectedWidgetBoundingBox = useMemo(() => {
        console.log('editorSelection.widgetId: ', widgetId)
        const selectedElement = document.querySelector(`[data-widget-id="${widgetId}"]`)
        if (selectedElement) {
            console.log('Rect get')
            //@ts-ignore getBoundingClientRect exists.
            const rect = selectedElement?.childNodes[0]?.getBoundingClientRect()
            console.log('Rect:', rect)
            return rect
        }
        return null
    }, [widgetId])

    const handleDuplicate = () => {
        duplicateWidget(widgetId)
    }
    const handleDelete = () => {
        deleteWidget(widgetId)
    }

    console.log('selectedWidgetBoundingBox: ', selectedWidgetBoundingBox)

    if (!selectedWidgetBoundingBox) {
        return <></>
    }

    return (
        <div className={` flex items-center gap-4  text-sm text-white rounded-lg dark`}>
            <CopyIcon onClick={handleDuplicate} className="h-4 w-4 cursor-pointer" />
            <TrashIcon onClick={handleDelete} className="h-4 w-4 cursor-pointer" />
            <SelectedGameWidgetScreenPicker widgetId={widgetId} />
        </div>
    )
}

const SelectedGameWidgetScreenPicker = ({ widgetId }: { widgetId: string }) => {
    const { rootWidget, findWidgetById, selectWidget } = useEditor()
    const selectedWidget = findWidgetById(rootWidget, widgetId)
    const [selectedScreen, setSelectedScreen] = useAtom(gamePreviewScreenAtomFamily(widgetId))

    const { settings } = useWidgetSettings(selectedWidget)
    const gameModule = useMemo(() => getGameModule(settings?.gameId), [settings?.gameId])
    const { flows } = useCampaignEditor()




    const handlePreviewSceneChanged = (screenId: string) => {
        if (!screenId) {
            return
        }
        //@ts-ignore Check why is this an error.
        setSelectedScreen(screenId)
    }

    // Flatten screens and sub-screens for display
    const flattenScreens = (screens: any[], parentScreen?: any): any[] => {
        const result: any[] = []

        screens.forEach(screen => {
            // Add main screen
            if (!settings.gameConfig || !screen.visibleCheck || screen.visibleCheck(settings.gameConfig)) {
                result.push({
                    ...screen,
                    isSubScreen: false,
                    parentScreen: parentScreen
                })

                // Add sub-screens if they exist
                if (screen.subScreens && Array.isArray(screen.subScreens)) {
                    screen.subScreens.forEach(subScreen => {
                        if (!settings.gameConfig || !subScreen.visibleCheck || subScreen.visibleCheck(settings.gameConfig)) {
                            result.push({
                                ...subScreen,
                                isSubScreen: true,
                                parentScreen: screen
                            })
                        }
                    })
                }
            }
        })

        return result
    }

    const effectiveScreens = useMemo(() => {
        const gameFlowId = settings?.gameConfig?.gameFlowId
        const flowGraph = flows.find(f => f.id === gameFlowId)

        let baseScreens = gameModule?.previewScreens || []
        const flowScreens = getAllFlowGameScreens(flowGraph)

        const flowScreenDefinitions = flowScreens.map(screen => ({
            screenId: screen.id,
            displayName: screen.name,
        }))

        console.log("Flow screens: ", flowScreens)

        return [...baseScreens, ...flowScreenDefinitions]
    }, [gameModule, settings?.gameConfig, flows])

    if (!effectiveScreens || !Array.isArray(effectiveScreens)) {
        return null
    }

    const flatScreens = flattenScreens(effectiveScreens as any)
    const defaultScreen = flatScreens[0]?.screenId

    return (
        <Select value={selectedScreen || defaultScreen} onValueChange={handlePreviewSceneChanged}>
            <SelectTrigger className="w-[220px]">
                <SelectValue placeholder="Select preview..." />
            </SelectTrigger>
            <SelectContent>
                {flatScreens.map((screen) => (
                    <SelectItem key={screen.screenId} value={screen.screenId}>
                        <span className={screen.isSubScreen ? "ml-4 text-muted-foreground" : ""}>
                            {screen.isSubScreen && "└ "}
                            {screen.displayName}
                        </span>
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    )
}


const WidgetOverlays = ({ hoveredWidgetId }: { hoveredWidgetId: string }) => {
    const { editorSelection } = useEditor()


    return (
        <>
            {editorSelection?.widgetId != hoveredWidgetId && <WidgetHoverOverlay widgetId={hoveredWidgetId} />}
            {editorSelection?.type == 'ui-widget' && <WidgetSelectionOverlay widgetId={editorSelection.widgetId} />}
            {editorSelection?.type == 'game-asset' && <GameAssetSelectionOverlay selection={editorSelection} />}
        </>
    )
}

const WidgetSelectionOverlay = ({ widgetId }: { widgetId: string; }) => {
    const { rootWidget, findWidgetById } = useEditor()

    const widget = useMemo(() => {
        return findWidgetById(rootWidget, widgetId)
    }, [rootWidget, widgetId])


    const elementDom = useMemo(() => {
        return document.querySelector(`[data-widget-id="${widgetId}"]`)?.childNodes[0]
    }, [widget])

    const widgetBounds = useMemo(() => {
        if (elementDom) {
            //@ts-ignore getBoundingClientRect exists.
            return elementDom.getBoundingClientRect()
        }
    }, [elementDom])

    const calculatedBounds = useMemo(() => {
        return {
            top: widgetBounds?.top + window.scrollY,
            left: widgetBounds?.left + window.scrollX,
            width: widgetBounds?.width,
            height: widgetBounds?.height
        }
    }, [widgetBounds])

    if (!widgetBounds) {
        return null
    }

    return (
        <div className='absolute outline outline-2 outline-blue-400 z-[2] pointer-events-none dark'
            style={{ top: calculatedBounds?.top, left: calculatedBounds?.left, width: calculatedBounds?.width, height: calculatedBounds?.height }}>
            <div className='mt-[-50px] ms-[-6px] flex w-0 '>
                <div className='flex bg-card text-white text-sm rounded-sm  py-0.5 px-3  pointer-events-auto'>
                    <Toolbox widgetId={widgetId} />
                </div>
            </div>

        </div>
    )
}

const WidgetHoverOverlay = ({ widgetId }: { widgetId: string; }) => {
    const { rootWidget, findWidgetById } = useEditor()

    const widget = useMemo(() => {
        return findWidgetById(rootWidget, widgetId)
    }, [rootWidget, widgetId])

    const widgetBounds = useMemo(() => {
        const selectedElement = document.querySelector(`[data-widget-id="${widgetId}"]`)?.childNodes[0]
        if (selectedElement) {
            //@ts-ignore getBoundingClientRect exists.
            return selectedElement.getBoundingClientRect()
        }
        return null
    }, [widget])

    const calculatedBounds = useMemo(() => {
        return {
            top: widgetBounds?.top + window.scrollY,
            left: widgetBounds?.left + window.scrollX,
            width: widgetBounds?.width,
            height: widgetBounds?.height
        }
    }, [widgetBounds])

    if (!widgetBounds) {
        return null
    }

    return (
        <div className='absolute outline outline-1 outline-blue-400 z-[2] pointer-events-none'
            style={{ top: calculatedBounds?.top, left: calculatedBounds?.left, width: calculatedBounds?.width, height: calculatedBounds?.height }}>


            <div className='mt-[-30px] ms-[-6px] flex pointer-events-auto'>
                <div className='text-blue-400  text-sm rounded-sm  py-0.5 px-2'>
                    {widget.componentName}
                </div>
            </div>

        </div>
    )
}

const GameAssetSelectionOverlay = ({ selection }: { selection: GameAssetSelection }) => {
    const { rootWidget, findWidgetById } = useEditor()


    const calculatedBounds = useMemo(() => {
        return {
            top: selection.bounds.y + window.scrollY,
            left: selection.bounds.x + window.scrollX,
            width: selection.bounds.width,
            height: selection.bounds.height
        }
    }, [selection.bounds])

    return (
        <div className='absolute outline outline-2 outline-green-400 z-[10000] pointer-events-none'
            style={{ top: calculatedBounds?.top, left: calculatedBounds?.left, width: calculatedBounds?.width, height: calculatedBounds?.height }}>

            <div className='mt-[-30px] ms-[-6px] flex pointer-events-auto'>
                <div className='bg-green-400 text-white text-sm rounded-sm  py-0.5 px-2'>
                    {selection.assetKey}
                </div>
            </div>
        </div>
    )
}


export default EditorFrame
