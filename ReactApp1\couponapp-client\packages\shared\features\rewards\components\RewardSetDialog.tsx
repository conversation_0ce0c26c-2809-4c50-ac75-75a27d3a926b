import React from 'react'
import { Dialog, DialogContent } from '@repo/shared/components/ui/dialog'
import RewardSetManager from './RewardSetManager'
import type { RewardSet as DashboardRewardSet } from '../types'
import type { RewardSet as GameRewardSet } from '@repo/shared/lib/game/useGameRewardsDetails'
import { Button } from '@repo/shared/components/ui/button'

export interface RewardSetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  set: DashboardRewardSet | GameRewardSet | null | undefined
  onChange?: (next: DashboardRewardSet | GameRewardSet) => void
}

const RewardSetDialog: React.FC<RewardSetDialogProps> = ({ open, onOpenChange, set, onChange }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl w-[calc(100vw-2rem)] p-0 overflow-hidden [&>button]:hidden flex flex-col">
        <div className="flex-1 overflow-hidden">
          {set ? (
            <RewardSetManager set={set} onChange={onChange} />
          ) : (
            <div className="h-[400px] flex items-center justify-center text-muted-foreground">Loading rewards…</div>
          )}
        </div>
        <div className="py-4 px-6 flex justify-end border-t">
          <Button onClick={() => onOpenChange(false)}>Done</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default RewardSetDialog

