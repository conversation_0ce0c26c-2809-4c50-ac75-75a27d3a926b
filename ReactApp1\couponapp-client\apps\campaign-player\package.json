{"name": "apps-campaign-player", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build || node -e \"process.exit(0)\"", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@repo/shared": "*", "plausible-tracker": "^0.3.9", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.27.0"}, "devDependencies": {"@babel/plugin-syntax-decorators": "^7.25.9", "@eslint/js": "^9.13.0", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "globals": "^15.11.0", "typescript": "~5.6.2", "typescript-eslint": "^8.10.0", "vite": "^5.4.9"}}