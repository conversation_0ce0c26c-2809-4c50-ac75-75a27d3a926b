---
description: 
globs: *.ts,*.tsx
alwaysApply: false
---
<rule>
Applies to folder: /apps/games/
When you work with controls/config keys in a game (like scratch-to-win game or game-boilerplate or 2048 and more...)  keep in mind that we might need to also edit files like: config (that is main config file) as well as config key editor, main config editoir and the controls itself if the game is using this, usally located at game/src/components.
Need to look if scenes are using these changes too. You will need to check references to the changes.
</rule>

<rule>
Applies to folder: /apps/games/
APPLIES TO config.ts
IMPORTANT GUIDELINES FOR MODYFYING CONFIG.TS

When you add some type like "GameButtonStyle" make sure you implement it correctly. For example it might require special ConfigEditorType (gameConfig.ts shared file)
You might need to add a new ConfigEditorType for the new style definition and implement settings handler (and component) in ConfigKeyEditor. If you plan to  have different settings in new type definition then it is crucial, we must have correct editor for your newly created type.
</rule>

<rule>
Applies to: CREATING NEW GAME OR COPYING BOILERPLATE
When we create a game from a boilerplate you will need to look for usages, where the boilerplate was referenced, eg in our game registres, game selector widgets etc.

Rename everything in index file from game boilerplate to a new name of our new game (and in other places that apply, but not config.ts)

In game registry do not replace name of game boilerplate, but instead add a new entry of newly created game. Make sure we renamed our game in the new, copied game directory and these ids needs to match in index.ts file. Also, do not rename config.ts in that case and anything in config file.
</rule>

<rule>
Applies to folder: /apps/games/*
When we are editing UI of the game, eg using components etc make sure you add correct data attributes for each component that is related to a specific config key in config.ts
So for exmaple if you create a button, you need to set correct config key of it. Some components have it taking as prop already, but some might not if we create a custom component (lets say you create a simple div, then you need to use data-editor-selectable-key to inform our editor that this div is selectable)
</rule>

<rule>
Applies to widget settings components and config key editors.
Stop using || operator to fallback on numeric values. In most cases this will lead to an error where for example I want to set padding 0 then a fallbacks is being triggered because 0 acts as false. 
</rule>

<rule>
Applies when copying diectories or files.
Keep in mind that our root directory of this project is "C:\Users\<USER>\RiderProjects\ReactIdentity\ReactApp1\couponapp-client"
Keep in mind that we use Powershell on Windows 11.

When you copy for example a game, DO NOT use relative paths. Use absolute paths instead.
</rule>

<rule>
Applies to: Games and their state management
useGameState hook keeps things in local storage. Also, consider using useGameState hook that are leveraging useGameState, instead of directly using useGameState.
</rule>

<rule>
Keep in mind that we are using dark mode for our dashboard. Ensure colors properly fit in dark mode.
</rule>

<rule>
DO NOT try to run any npm build commands or run commands as well as dotnet migrations. 
Tell me that i could do that but do not run it please.
</rule>

<rule>
When creating/editing plan.md file for designing solutions tasks etc, make sure they will be minimal without unnecessary text. Only essential plan data should be there. I don't have time to read all shit you write there.
When I ask to create plan.md file, DO NOT do any other implementations. It means we are planning and we are not doing any coding yet. You cannot edit other files than plan.md.
You need to confirm and explicitly tell me we are in plan mode, you will write a non overhelming plan with essential data - all of this you must repeat to me so I know you understand.
</rule>