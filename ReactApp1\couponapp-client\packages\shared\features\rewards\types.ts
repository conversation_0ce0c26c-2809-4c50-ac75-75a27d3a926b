import type { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import type { Settings as CouponCodeReusableSettings } from './reward-types/couponCodeReusable'
import type { Settings as CouponCodeMultipleSettings } from './reward-types/couponCodeMultiple'
import type { ComponentType } from 'react'

export type RewardType = 'coupon-code-reusable' | 'coupon-code-multiple'

export interface RewardTypeToSettings {
  'coupon-code-reusable': CouponCodeReusableSettings
  'coupon-code-multiple': CouponCodeMultipleSettings
}

export type RewardSettings<T extends RewardType = RewardType> = RewardTypeToSettings[T]

export type RewardDefinition<T extends RewardType = RewardType> = {
  id: string
  name: string
  description: string
  image?: AssetUrl
  type: T
  settings: RewardSettings<T>
  dropRate: number
}

export type RewardSet = {
  id: string
  rewards: RewardDefinition[]
}

export interface EditorProps {
  reward: RewardDefinition
  onChange: (next: RewardDefinition) => void
}

export type EditorComponent = ComponentType<EditorProps>