import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
} from "react";
// import { useCurrentlyRenderedScene } from "@repo/shared/lib/hooks/useCurrentlyRenderedScene";
import { FlappyGame, FlappyPreviewScreen } from "./flappyGame";
import { AssetUrl } from "@repo/shared/lib/types/widgetSettings";
import { useAtom } from "jotai";
import {
  
  fontsLoadedAtom,
  gamePreviewScreenAtomFamily,
  selectedEditorItemAtom,
} from "@repo/shared/lib/atoms/editor-atoms";
import {
  FlappyBirdConfig,
  flappyBirdDefaultConfig,
} from "../types/flappyBirdConfig";
import { mergeConfigWithFallback } from "./utils/configUtils";

interface FlappyGameProps {
  config: FlappyBirdConfig;
  widgetId: string;
  resolveAssetUrl: (id: AssetUrl) => string;
  isPreview?: boolean;
}

//@ts-ignore just keep this.

const FlappyGameViewer: React.FC<FlappyGameProps> = ({
  config,
  widgetId,
  resolveAssetUrl,
  isPreview,
}) => {
  const gameConfig = useMemo(
    () => mergeConfigWithFallback(config, flappyBirdDefaultConfig),
    [config]
  );

  const containerRef = useRef<HTMLDivElement>(null);
  const gameInstanceRef = useRef<FlappyGame | null>(null);
  const [isGameInitialized, setIsGameInitialized] = useState(false);
  const [game, setGame] = useState<FlappyGame | null>(null);

  const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom);

  const [selectedScreen, setSelectedScreen] = useAtom(
    gamePreviewScreenAtomFamily(widgetId)
  );

  // const [fontsLoaded, setFontsLoaded] = useAtom(fontsLoadedAtom);
  // const { currentlyRenderedScene } = useCurrentlyRenderedScene();

  const handleAssetClick = useCallback(
    (
      assetKey: string,
      bounds: { x: number; y: number; width: number; height: number }
    ) => {
      if (!isPreview || !widgetId) return;

      setEditorSelection({
        widgetId,
        assetKey,
        bounds,
        type: "game-asset",
        time: Date.now(),
      });
    },
    [isPreview, widgetId, setEditorSelection]
  );

  useEffect(() => {
    if (!gameConfig || !widgetId) {
      console.log("No config or widgetid", gameConfig, widgetId);
      return;
    }

    if (!containerRef.current) {
      return;
    }

    async function init() {
      try {
        console.log("Initializing FlappyGame");
        const game = new FlappyGame(
          containerRef.current,
          gameConfig,
          widgetId,
          resolveAssetUrl,
          isPreview,
          handleAssetClick
        );

        gameInstanceRef.current = game;
        setGame(game);

        await game.init(isPreview, () => {
          setIsGameInitialized(true);
        });
        console.log("Game initialized");
      } catch (error) {
        console.error("Error initializing game:", error);
      }
    }

    init();

    // Cleanup function
    return () => {
      if (gameInstanceRef.current) {
        console.log("Destroying game");
        gameInstanceRef.current.destroy();
        // gameInstanceRef.current = null;
        // setGame(null);
      }
    };
  }, [
    // fontsLoaded,
    // currentlyRenderedScene,
    widgetId,
    // gameConfig,
    isPreview,
    handleAssetClick,
    resolveAssetUrl,
  ]);

  // useEffect(() => {
  //   if (!game) {
  //     return;
  //   }

  //   game.reloadFonts();
  // }, [fontsLoaded, game]);

  return (
    <>
      <div
        data-game-widget-id={widgetId}
        className="w-full h-full bg-black "
        ref={containerRef}
      >
        {!isGameInitialized && (
          <div className=" flex flex-grow items-center justify-center text-white ">
            Initializing game
          </div>
        )}
      </div>
    </>
  );
};

export default FlappyGameViewer;
